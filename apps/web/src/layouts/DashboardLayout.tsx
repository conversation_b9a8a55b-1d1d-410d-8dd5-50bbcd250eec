import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { UserProfileHeader } from "@/components/dashboard/UserProfileHeader";
import { Toaster } from "@/components/ui/toaster";
import { useScreenSize } from "@/hooks/use-mobile";
import { useEffect, useState } from "react";
import { Outlet } from "react-router-dom";

export function DashboardLayout() {
  const screenSize = useScreenSize();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Handle sidebar state based on screen size
  useEffect(() => {
    if (screenSize === 'mobile') {
      setSidebarOpen(false);
    } else if (screenSize === 'tablet') {
      setSidebarOpen(false); // Start collapsed on tablet
    } else {
      setSidebarOpen(true); // Full sidebar on desktop
    }
  }, [screenSize]);

  const handleToggleSidebar = () => {
    if (screenSize === 'mobile') {
      setMobileMenuOpen(!mobileMenuOpen);
    } else {
      setSidebarOpen(!sidebarOpen);
    }
  };

  return (
    <div className="absolute inset-0 flex bg-background">
      {/* Sidebar */}
      <DashboardSidebar
        open={sidebarOpen}
        setOpen={setSidebarOpen}
        mobileMenuOpen={mobileMenuOpen}
        setMobileMenuOpen={setMobileMenuOpen}
        screenSize={screenSize}
      />

      {/* Mobile overlay */}
      {screenSize === 'mobile' && mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Main content */}
      <div className="flex flex-col flex-1 min-w-0">
        {/* Top navigation */}
        <UserProfileHeader toggleSidebar={handleToggleSidebar} />

        {/* Main content area */}
        <main className="flex-1 overflow-auto custom-scrollbar">
          <div className="p-4 sm:p-6">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Global UI elements */}
      <Toaster />
    </div>
  );
}
