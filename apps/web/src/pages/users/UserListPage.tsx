import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { supabase } from "@/lib/supabase";
import { ArrowLeft, Edit, Mail, Search, Shield, Users } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

interface UserWithRoles {
  id: string;
  email: string;
  user_metadata: {
    first_name?: string;
    last_name?: string;
    name?: string;
  } | null;
  created_at: string;
  last_sign_in_at: string | null;
  roles: Array<{
    role: string;
    organization_name: string | null;
    organization_type: string | null;
    department_name: string | null;
  }>;
}

export function UserListPage() {
  const { user } = useAuth();
  const { isSystemAdmin, isOrgAdmin } = useUserRoles();
  const navigate = useNavigate();
  const [users, setUsers] = useState<UserWithRoles[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserWithRoles[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  const canManageUsers = isSystemAdmin || isOrgAdmin;

  // Redirect if user doesn't have permission
  useEffect(() => {
    if (!canManageUsers) {
      navigate("/dashboard");
      toast.error("You don't have permission to manage users");
    }
  }, [canManageUsers, navigate]);

  const fetchUsers = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Fetch users from auth.users
      const { data: authUsers, error: authError } = await supabase
        .from("users")
        .select("id, email, raw_user_meta_data, created_at, last_sign_in_at")
        .order("created_at", { ascending: false });

      if (authError) throw authError;

      // Fetch user roles with organization info
      const { data: userRoles, error: rolesError } = await supabase
        .from("user_roles")
        .select(`
          user_id,
          role,
          organization:organizations(name, type),
          department:departments(name)
        `);

      if (rolesError) throw rolesError;

      // Combine the data
      const usersWithRoles: UserWithRoles[] = (authUsers || []).map((authUser) => {
        const roles = (userRoles || [])
          .filter((role) => role.user_id === authUser.id)
          .map((role) => ({
            role: role.role,
            organization_name: role.organization?.name || null,
            organization_type: role.organization?.type || null,
            department_name: role.department?.name || null,
          }));

        return {
          id: authUser.id,
          email: authUser.email,
          user_metadata: authUser.raw_user_meta_data as any,
          created_at: authUser.created_at,
          last_sign_in_at: authUser.last_sign_in_at,
          roles,
        };
      });

      setUsers(usersWithRoles);
      setFilteredUsers(usersWithRoles);
    } catch (err) {
      console.error("Error fetching users:", err);
      toast.error("Failed to load users");
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Filter users based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter((user) => {
        const name = user.user_metadata?.first_name && user.user_metadata?.last_name
          ? `${user.user_metadata.first_name} ${user.user_metadata.last_name}`
          : user.user_metadata?.name || "";
        
        return (
          user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.roles.some(role => 
            role.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
            role.organization_name?.toLowerCase().includes(searchTerm.toLowerCase())
          )
        );
      });
      setFilteredUsers(filtered);
    }
  }, [searchTerm, users]);

  const getUserDisplayName = (user: UserWithRoles) => {
    if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
      return `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
    }
    if (user.user_metadata?.name) {
      return user.user_metadata.name;
    }
    return user.email.split('@')[0];
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "system_admin":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "org_admin":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case "clinical_admin":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "physician":
      case "nurse_practitioner":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "registered_nurse":
      case "medical_assistant":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  if (!canManageUsers) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate("/users/manage")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to User Management
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">All Users</h1>
          <p className="text-muted-foreground mt-1">
            {isSystemAdmin
              ? "Manage all users across the system"
              : "Manage users in your organizations"}
          </p>
          {isSystemAdmin && (
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="destructive" className="text-xs">
                System Administrator
              </Badge>
              <span className="text-xs text-muted-foreground">
                Full user management access
              </span>
            </div>
          )}
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => fetchUsers()} disabled={isLoading}>
            <Search className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button onClick={() => navigate("/users/invite")}>
            <Mail className="mr-2 h-4 w-4" />
            Invite User
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Summary Stats */}
      {isSystemAdmin && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Total Users</p>
                  <p className="text-2xl font-bold">{filteredUsers.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-sm font-medium">System Admins</p>
                  <p className="text-2xl font-bold">
                    {filteredUsers.filter(u => u.roles.some(r => r.role === 'system_admin')).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-sm font-medium">Org Admins</p>
                  <p className="text-2xl font-bold">
                    {filteredUsers.filter(u => u.roles.some(r => r.role === 'org_admin')).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Clinical Staff</p>
                  <p className="text-2xl font-bold">
                    {filteredUsers.filter(u => u.roles.some(r => ['physician', 'nurse_practitioner', 'registered_nurse'].includes(r.role))).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Users ({filteredUsers.length})
          </CardTitle>
          <CardDescription>
            Manage user accounts, roles, and access permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 10 }).map((_, i) => (
                <div key={i} className="h-16 bg-muted rounded-md animate-pulse" />
              ))}
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm ? "No users found matching your search" : "No users found"}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Roles</TableHead>
                  <TableHead>Last Sign In</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{getUserDisplayName(user)}</p>
                        <p className="text-sm text-muted-foreground">
                          Joined {new Date(user.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{user.email}</span>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {user.roles.length === 0 ? (
                          <Badge variant="outline" className="text-xs">
                            No roles assigned
                          </Badge>
                        ) : (
                          user.roles.map((role, index) => (
                            <Badge
                              key={index}
                              className={`text-xs ${getRoleColor(role.role)}`}
                              title={role.organization_name || "System-wide"}
                            >
                              {role.role.replace(/_/g, " ")}
                              {role.organization_name && (
                                <span className="ml-1 opacity-75">
                                  @ {role.organization_name}
                                </span>
                              )}
                            </Badge>
                          ))
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {user.last_sign_in_at
                          ? new Date(user.last_sign_in_at).toLocaleDateString()
                          : "Never"}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/users/${user.id}/edit`)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/users/${user.id}/roles`)}
                        >
                          <Shield className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
