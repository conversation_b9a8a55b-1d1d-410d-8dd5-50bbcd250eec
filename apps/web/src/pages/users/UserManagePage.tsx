import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { Key, Settings, Shield, UserPlus, Users } from "lucide-react";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export function UserManagePage() {
  const { user } = useAuth();
  const { isSystemAdmin, isOrgAdmin } = useUserRoles();
  const navigate = useNavigate();

  const canManageUsers = isSystemAdmin || isOrgAdmin;

  // Redirect if user doesn't have permission
  useEffect(() => {
    if (!canManageUsers) {
      navigate("/dashboard");
      toast.error("You don't have permission to manage users");
    }
  }, [canManageUsers, navigate]);

  if (!canManageUsers) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">User Management</h1>
          <p className="text-muted-foreground mt-1">
            {isSystemAdmin
              ? "System-wide user, role, and permission management"
              : "Manage users and roles for your organizations"}
          </p>
          {isSystemAdmin && (
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="destructive" className="text-xs">
                System Administrator
              </Badge>
              <span className="text-xs text-muted-foreground">
                Full system access
              </span>
            </div>
          )}
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate("/users/invite")}>
            <UserPlus className="mr-2 h-4 w-4" />
            Invite User
          </Button>
          <Button onClick={() => navigate("/users/create")}>
            <Users className="mr-2 h-4 w-4" />
            Create User
          </Button>
        </div>
      </div>

      {/* Management Tabs */}
      <Tabs defaultValue="users" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Users
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            Permissions
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage user accounts, assignments, and access levels
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="cursor-pointer hover:bg-muted/50" onClick={() => navigate("/users/list")}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Users className="h-8 w-8 text-blue-500" />
                      <div>
                        <h3 className="font-semibold">All Users</h3>
                        <p className="text-sm text-muted-foreground">
                          View and manage all user accounts
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:bg-muted/50" onClick={() => navigate("/users/assignments")}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Shield className="h-8 w-8 text-green-500" />
                      <div>
                        <h3 className="font-semibold">Role Assignments</h3>
                        <p className="text-sm text-muted-foreground">
                          Assign roles to users across organizations
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:bg-muted/50" onClick={() => navigate("/users/bulk")}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <UserPlus className="h-8 w-8 text-purple-500" />
                      <div>
                        <h3 className="font-semibold">Bulk Operations</h3>
                        <p className="text-sm text-muted-foreground">
                          Import users and bulk role assignments
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Roles Tab */}
        <TabsContent value="roles" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Role Management</CardTitle>
              <CardDescription>
                Define and manage user roles and their capabilities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="cursor-pointer hover:bg-muted/50" onClick={() => navigate("/roles/definitions")}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Shield className="h-8 w-8 text-blue-500" />
                      <div>
                        <h3 className="font-semibold">Role Definitions</h3>
                        <p className="text-sm text-muted-foreground">
                          Create and edit role definitions
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:bg-muted/50" onClick={() => navigate("/roles/hierarchy")}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Users className="h-8 w-8 text-green-500" />
                      <div>
                        <h3 className="font-semibold">Role Hierarchy</h3>
                        <p className="text-sm text-muted-foreground">
                          Visualize and manage role relationships
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:bg-muted/50" onClick={() => navigate("/roles/templates")}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Settings className="h-8 w-8 text-purple-500" />
                      <div>
                        <h3 className="font-semibold">Role Templates</h3>
                        <p className="text-sm text-muted-foreground">
                          Pre-configured role templates
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Permission Management</CardTitle>
              <CardDescription>
                Configure granular permissions and access controls
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="cursor-pointer hover:bg-muted/50" onClick={() => navigate("/permissions/resources")}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Key className="h-8 w-8 text-blue-500" />
                      <div>
                        <h3 className="font-semibold">Resource Permissions</h3>
                        <p className="text-sm text-muted-foreground">
                          Manage permissions by resource type
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:bg-muted/50" onClick={() => navigate("/permissions/conditions")}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Settings className="h-8 w-8 text-green-500" />
                      <div>
                        <h3 className="font-semibold">Conditional Access</h3>
                        <p className="text-sm text-muted-foreground">
                          Set up conditional permission rules
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:bg-muted/50" onClick={() => navigate("/permissions/audit")}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Shield className="h-8 w-8 text-purple-500" />
                      <div>
                        <h3 className="font-semibold">Permission Audit</h3>
                        <p className="text-sm text-muted-foreground">
                          Audit and test permission configurations
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>User Management Settings</CardTitle>
              <CardDescription>
                Configure user management policies and defaults
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Settings and configuration options will be available here.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
