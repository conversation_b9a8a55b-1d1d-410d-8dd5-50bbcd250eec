import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { supabase } from "@/lib/supabase";
import { ArrowLeft, Edit, Plus, Search, Shield, Trash2 } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

interface RoleDefinition {
  role: string;
  description: string;
  base_permissions: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export function RoleDefinitionsPage() {
  const { user } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();
  const [roles, setRoles] = useState<RoleDefinition[]>([]);
  const [filteredRoles, setFilteredRoles] = useState<RoleDefinition[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Redirect if user doesn't have permission
  useEffect(() => {
    if (!isSystemAdmin) {
      navigate("/dashboard");
      toast.error("You don't have permission to manage role definitions");
    }
  }, [isSystemAdmin, navigate]);

  const fetchRoles = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("role_definitions")
        .select("*")
        .order("role");

      if (error) throw error;

      setRoles(data || []);
      setFilteredRoles(data || []);
    } catch (err) {
      console.error("Error fetching roles:", err);
      toast.error("Failed to load role definitions");
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchRoles();
  }, [fetchRoles]);

  // Filter roles based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredRoles(roles);
    } else {
      const filtered = roles.filter(
        (role) =>
          role.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
          role.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredRoles(filtered);
    }
  }, [searchTerm, roles]);

  const handleDeleteRole = async (roleId: string) => {
    if (!confirm("Are you sure you want to delete this role? This action cannot be undone.")) {
      return;
    }

    try {
      const { error } = await supabase
        .from("role_definitions")
        .delete()
        .eq("role", roleId);

      if (error) throw error;

      toast.success("Role deleted successfully");
      fetchRoles();
    } catch (err) {
      console.error("Error deleting role:", err);
      toast.error("Failed to delete role");
    }
  };

  const getPermissionSummary = (permissions: Record<string, any>) => {
    const keys = Object.keys(permissions);
    if (keys.includes("all") && permissions.all) {
      return "Full System Access";
    }
    if (keys.includes("multi_org") && permissions.multi_org) {
      return "Multi-Organization Access";
    }
    return `${keys.length} permission${keys.length !== 1 ? 's' : ''}`;
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "system_admin":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "org_admin":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case "clinical_admin":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "physician":
      case "nurse_practitioner":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "registered_nurse":
      case "medical_assistant":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  if (!isSystemAdmin) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate("/users/manage")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to User Management
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Role Definitions</h1>
          <p className="text-muted-foreground mt-1">
            Define and manage user roles and their base permissions
          </p>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="destructive" className="text-xs">
              System Administrator
            </Badge>
            <span className="text-xs text-muted-foreground">
              Role management requires system admin access
            </span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => fetchRoles()} disabled={isLoading}>
            <Search className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button onClick={() => navigate("/roles/create")}>
            <Plus className="mr-2 h-4 w-4" />
            Create Role
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search roles..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Roles Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Role Definitions ({filteredRoles.length})
          </CardTitle>
          <CardDescription>
            Manage system-wide role definitions and their base permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="h-12 bg-muted rounded-md animate-pulse" />
              ))}
            </div>
          ) : filteredRoles.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm ? "No roles found matching your search" : "No role definitions found"}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Role</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Permissions</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRoles.map((role) => (
                  <TableRow key={role.role}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge className={getRoleColor(role.role)}>
                          {role.role.replace(/_/g, " ")}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-md">
                        <p className="text-sm">{role.description}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {getPermissionSummary(role.base_permissions)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {new Date(role.created_at).toLocaleDateString()}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/roles/${role.role}/edit`)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteRole(role.role)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
