import { createCache<PERSON><PERSON>, requestDeduplicator } from "@/lib/request-deduplication";
import { supabase } from "@/lib/supabase";
import type { Database } from "@spritely/supabase-types";
import { User } from "@supabase/supabase-js";

type Organization = Database["public"]["Tables"]["organizations"]["Row"];

export async function getUserOrganizations(user: User): Promise<Organization[]> {
  return requestDeduplicator.deduplicate(
    createCacheKey.organizations(user.id),
    async () => {
      const userRoles = await getUserRoles(user.id);

      if (!userRoles?.length) return [];

      const isSystemAdmin = userRoles.some(role => role.role === "system_admin");

  if (isSystemAdmin) {
    // System admin gets all organizations with hierarchy data
    const { data } = await supabase
      .from("organizations")
      .select("*")
      .order("hierarchy_level", { ascending: true })
      .order("name", { ascending: true });

    const allOrgs = data || [];

    // Add a virtual "All Organizations" entry for system admins - this should be the DEFAULT
    const allOrganizationsEntry: Organization = {
      id: "system-admin-all-orgs",
      name: "All Organizations",
      type: "system",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      owner_id: user.id,
      settings: {
        description: "System Administrator view of all organizations"
      },
      billing_info: null,
      subscription_tier: "enterprise",
      parent_id: null,
      hierarchy_level: -1,
      hierarchy_path: "system-admin-all-orgs"
    };

    return [allOrganizationsEntry, ...allOrgs];
  }

  // Regular user gets their organizations with hierarchy data
  const orgIds = userRoles.map(role => role.organization_id).filter(Boolean) as string[];
  if (!orgIds.length) return [];

  const { data } = await supabase
    .from("organizations")
    .select("*")
    .in("id", orgIds)
    .order("hierarchy_level", { ascending: true })
    .order("name", { ascending: true });

      return data || [];
    }
  );
}

export async function getOrganizationHierarchy(user: User): Promise<Organization[]> {
  return requestDeduplicator.deduplicate(
    createCacheKey.organizationHierarchy(user.id),
    async () => {
      const userRoles = await getUserRoles(user.id);
      if (!userRoles?.length) return [];

      const isSystemAdmin = userRoles.some(role => role.role === "system_admin");

      if (isSystemAdmin) {
        // System admin gets organizations based on their role assignments
        // If they have system_admin role on a specific organization, show that hierarchy
        // If they have system_admin role without organization, show all organizations

        const systemAdminRoles = userRoles.filter(role => role.role === "system_admin");
        const systemAdminOrgIds = systemAdminRoles
          .map(role => role.organization_id)
          .filter(Boolean) as string[];

        let allOrgs: Organization[] = [];

        if (systemAdminOrgIds.length > 0) {
          // System admin has specific organization assignments
          // Get the organizations and their hierarchies
          const { data: rootOrgs } = await supabase
            .from("organizations")
            .select("*")
            .in("id", systemAdminOrgIds);

          if (rootOrgs?.length) {
            // For each root org, get its entire hierarchy
            const hierarchyQueries = rootOrgs.map(async (org) => {
              const { data } = await supabase
                .from("organizations")
                .select("*")
                .or(`id.eq.${org.id},hierarchy_path.like.${org.hierarchy_path || org.id}.%`);
              return data || [];
            });

            const hierarchyResults = await Promise.all(hierarchyQueries);
            const uniqueOrgs = new Map<string, Organization>();

            hierarchyResults.flat().forEach(org => {
              uniqueOrgs.set(org.id, org);
            });

            allOrgs = Array.from(uniqueOrgs.values())
              .sort((a, b) => {
                if (a.hierarchy_level !== b.hierarchy_level) {
                  return a.hierarchy_level - b.hierarchy_level;
                }
                return a.name.localeCompare(b.name);
              });
          }
        } else {
          // System admin without specific organization - show all organizations
          const { data } = await supabase
            .from("organizations")
            .select("*")
            .order("hierarchy_level", { ascending: true })
            .order("name", { ascending: true });

          allOrgs = data || [];
        }

        // Add virtual "All Organizations" entry only if they can see multiple org hierarchies
        const shouldShowAllOrgsEntry = systemAdminOrgIds.length === 0 ||
          allOrgs.filter(org => org.hierarchy_level === 0).length > 1;

        if (shouldShowAllOrgsEntry) {
          const allOrganizationsEntry: Organization = {
            id: "system-admin-all-orgs",
            name: "All Organizations",
            type: "system",
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            owner_id: user.id,
            settings: { description: "System Administrator view of all organizations" },
            billing_info: null,
            subscription_tier: "enterprise",
            ...(allOrgs.length > 0 && allOrgs[0].parent_id !== undefined ? {
              parent_id: null,
              hierarchy_level: -1,
              hierarchy_path: "system-admin-all-orgs"
            } : {})
          } as Organization;

          return [allOrganizationsEntry, ...allOrgs];
        }

        return allOrgs;
      }

      // Regular user gets their organizations with hierarchy
      const orgIds = userRoles.map(role => role.organization_id).filter(Boolean) as string[];
      if (!orgIds.length) return [];

      const { data } = await supabase
        .from("organizations")
        .select("*")
        .in("id", orgIds)
        .order("hierarchy_level", { ascending: true })
        .order("name", { ascending: true });

      return data || [];
    },
    60 * 1000 // Cache for 1 minute
  );
}

// Unified user roles fetcher that gets all user role data at once
export async function getUserRoles(userId: string): Promise<Array<{role: string, organization_id: string | null}>> {
  return requestDeduplicator.deduplicate(
    createCacheKey.userRoles(userId),
    async () => {
      const { data, error } = await supabase
        .from("user_roles")
        .select("role, organization_id")
        .eq("user_id", userId);

      if (error) {
        throw new Error(`Failed to fetch user roles: ${error.message}`);
      }

      return data || [];
    },
    60 * 1000 // Cache for 1 minute since this rarely changes
  );
}

export async function isUserSystemAdmin(userId: string): Promise<boolean> {
  const roles = await getUserRoles(userId);
  return roles.some(role => role.role === "system_admin");
}

export async function hasUserOrganizations(userId: string): Promise<boolean> {
  const roles = await getUserRoles(userId);
  return roles.some(role => role.organization_id !== null);
}

export async function getOrganizationInvites(userEmail: string): Promise<Array<{ id: string }>> {
  return requestDeduplicator.deduplicate(
    createCacheKey.organizationInvites(userEmail),
    async () => {
      const { data, error } = await supabase
        .from("organization_invites")
        .select("id")
        .eq("email", userEmail)
        .eq("status", "pending")
        .limit(1);

      if (error) {
        console.error("Error checking invites:", error);
        return [];
      }

      return data || [];
    },
    30 * 1000 // Cache for 30 seconds
  );
}

export async function getDetailedOrganizationInvites(userEmail: string): Promise<Array<{
  id: string;
  role: string;
  expires_at: string;
  organization: {
    id: string;
    name: string;
    type: string;
  };
}>> {
  return requestDeduplicator.deduplicate(
    createCacheKey.organizationInvitesDetailed(userEmail),
    async () => {
      const { data, error } = await supabase
        .from("organization_invites")
        .select(`
          id,
          role,
          expires_at,
          organization:organizations (
            id,
            name,
            type
          )
        `)
        .eq("email", userEmail)
        .eq("status", "pending")
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      // Filter and transform the data to match the expected type
      const filteredData = (data || []).filter(item =>
        item.expires_at !== null && item.organization !== null
      ).map(item => ({
        id: item.id,
        role: item.role,
        expires_at: item.expires_at!,
        organization: item.organization!
      }));

      return filteredData;
    },
    30 * 1000 // Cache for 30 seconds
  );
}